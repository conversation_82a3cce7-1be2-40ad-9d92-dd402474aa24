/**
 * @description This component renders a comprehensive pricing comparison table for Klusgebied subscription services. It showcases three distinct subscription tiers (Klusgebied+, Klusgebied Pro, Klusgebied Partner) with detailed feature comparisons in a responsive table format. The component includes professional styling, clear visual indicators for included/excluded features, and call-to-action buttons for each tier. It's designed to be fully responsive with horizontal scrolling on mobile devices.
 */

import React, { useState } from "react";
import {
  CheckCircle,
  ArrowRight,
  X,
  Loader,
  AlertTriangle,
  User,
  Mail,
  Check,
  Star,
  Crown,
} from "lucide-react";
import { useNavigate } from "react-router-dom";

const InvestmentSection = () => {
  const navigate = useNavigate();
  const [showModal, setShowModal] = useState(false);
  const [selectedPackage, setSelectedPackage] = useState(null);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState(""); // 'success', 'error', 'duplicate'

  const subscriptionTiers = [
    {
      id: "klusgebied-plus",
      name: "Klusgebied+",
      price: 149,
      period: "p/m",
      color: "blue",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200",
      buttonColor: "bg-blue-500 hover:bg-blue-600",
      textColor: "text-blue-600",
      icon: Star,
    },
    {
      id: "klusgebied-pro",
      name: "Klusgebied Pro",
      price: 299,
      period: "p/m",
      color: "teal",
      bgColor: "bg-teal-50",
      borderColor: "border-teal-200",
      buttonColor: "bg-teal-500 hover:bg-teal-600",
      textColor: "text-teal-600",
      popular: true,
      icon: CheckCircle,
    },
    {
      id: "klusgebied-partner",
      name: "Klusgebied Partner",
      price: 499,
      period: "p/m",
      color: "purple",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200",
      buttonColor: "bg-purple-500 hover:bg-purple-600",
      textColor: "text-purple-600",
      icon: Crown,
    },
  ];

  const comparisonFeatures = [
    {
      feature: "Topvermelding",
      plus: "1 regio",
      pro: "3 regio's",
      partner: "3 regio's",
    },
    {
      feature: "Aantal reacties op klussen",
      plus: "5 per maand",
      pro: "Onbeperkt",
      partner: "Onbeperkt",
    },
    {
      feature: "Profieloptimalisatie",
      plus: { icon: "Check" },
      pro: { icon: "Check" },
      partner: { icon: "Check" },
    },
    {
      feature: "Social media shout-out",
      plus: "1x per kwartaal",
      pro: "1x per maand",
      partner: "1x per maand",
    },
    {
      feature: "Nieuwsbriefvermelding",
      plus: "1x per jaar",
      pro: "3x per jaar",
      partner: "Doorlopend min. 6x per jaar",
    },
    {
      feature: "Toegang tot Klusgebied Community",
      plus: { icon: "Check" },
      pro: { icon: "Check" },
      partner: { icon: "Check" },
    },
    {
      feature: "Support levels",
      plus: "E-mail/chat 48u",
      pro: "Prioriteitssupport",
      partner: "Persoonlijke accountmanager",
    },
    {
      feature: "Groeigesprek video call",
      plus: { icon: "X" },
      pro: "1x per kwartaal",
      partner: "1x per kwartaal + op aanvraag",
    },
    {
      feature: "Professionele profielvideo 1 min",
      plus: { icon: "X" },
      pro: { icon: "Check" },
      partner: { icon: "Check" },
    },
    {
      feature: "Professionele fotoshoot op locatie",
      plus: { icon: "X" },
      pro: { icon: "X" },
      partner: { icon: "Check" },
    },
    {
      feature: "Bedrijfsvideo 60 sec",
      plus: { icon: "X" },
      pro: { icon: "X" },
      partner: { icon: "Check" },
    },
    {
      feature: "Eigen website op maat",
      plus: { icon: "X" },
      pro: { icon: "X" },
      partner: { icon: "Check" },
    },
    {
      feature: "Domeinnaam + professioneel e-mailadres",
      plus: { icon: "X" },
      pro: { icon: "X" },
      partner: { icon: "Check" },
    },
    {
      feature: "Hosting & technisch onderhoud 1 jaar inbegrepen",
      plus: { icon: "X" },
      pro: { icon: "X" },
      partner: { icon: "Check" },
    },
    {
      feature: "Google Mijn Bedrijf registratie + SEO-optimalisatie",
      plus: { icon: "X" },
      pro: { icon: "X" },
      partner: { icon: "Check" },
    },
    {
      feature: "Maandelijkse advertenties via Klusgebied",
      plus: { icon: "X" },
      pro: "€50 inbegrepen",
      partner: "€100 inbegrepen",
    },
    {
      feature: "Regio-exclusiviteit 6 maanden",
      plus: { icon: "X" },
      pro: { icon: "X" },
      partner: { icon: "Check" },
    },
    {
      feature: '"Founding Partner" badge',
      plus: { icon: "Check" },
      pro: { icon: "Check" },
      partner: { icon: "Check", text: "lifetime" },
    },
    {
      feature: 'Vermelding op "Topvakmannen"-pagina',
      plus: { icon: "X" },
      pro: { icon: "X" },
      partner: { icon: "Check" },
    },
    {
      feature: "Looptijd abonnement",
      plus: "12 maanden",
      pro: "12 maanden",
      partner: "Minimaal 6 maanden",
    },
    {
      feature: "Voordeel bij jaarbetaling",
      plus: "1 maand gratis",
      pro: "Eerste 3 maanden voor €199",
      partner: "2 maanden gratis + gratis brochure",
    },
  ];

  const renderCell = (value: any) => {
    if (typeof value === "object" && value !== null) {
      if (value.icon === "Check") {
        return (
          <div className="flex items-center justify-center">
            <Check className="w-5 h-5 text-teal-500" />
            {value.text && (
              <span className="ml-2 text-sm text-gray-600">{value.text}</span>
            )}
          </div>
        );
      }
      if (value.icon === "X") {
        return (
          <div className="flex items-center justify-center">
            <X className="w-5 h-5 text-red-400" />
          </div>
        );
      }
    }
    return <span className="text-gray-700">{value}</span>;
  };

  const handlePackageSelect = (pkg: any) => {
    setSelectedPackage(pkg);
    setShowModal(true);
    setSubmitStatus("");
    setFormData({ name: "", email: "" });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleSubmit = async (e) => {
    // e.preventDefault();
    // if (!selectedPackage || !formData.name || !formData.email) return;
    // setIsSubmitting(true);
    // setSubmitStatus("");
    // try {
    //   // 1. Check for duplicate submissions
    //   const { data: existingSubmissions } = await supabase
    //     .from("6865560a10605c25f99173ec_investment_submissions")
    //     .select("*")
    //     .eq("email", formData.email)
    //     .eq("package_name", selectedPackage.name)
    //     .eq("email_sent", true);
    //   if (existingSubmissions && existingSubmissions.length > 0) {
    //     setSubmitStatus("duplicate");
    //     setIsSubmitting(false);
    //     return;
    //   }
    //   // 2. Send email notification
    //   const emailSubject = `[Klusgebied+] Nieuwe Investeringsinteresse: ${selectedPackage.name}`;
    //   const emailHtml = `
    //     <div style="font-family: Arial, sans-serif; max-width:600px; margin:auto; border:1px solid #e0e0e0; border-radius:8px; overflow:hidden;">
    //       <div style="background:#00B894; color:#fff; padding:16px; text-align:center;">
    //         <img src="https://heyboss.heeyo.ai/user-assets/b33e8421-fd4d-4f27-8ad6-0b128873941c (1)_LRfmi8Vt.png" alt="Klusgebied Logo" style="height:40px; margin-bottom:8px;">
    //         <h2 style="margin:0;">Klusgebied+</h2>
    //       </div>
    //       <div style="padding:24px; background:#fafafa;">
    //         <h3 style="color:#00B894; margin-top:0;">Nieuwe Investeringsinteresse</h3>
    //         <table style="width:100%; border-collapse:collapse;">
    //           <tr><td style="padding:8px 0; border-bottom:1px solid #eee;"><strong>Naam:</strong></td><td style="padding:8px 0; border-bottom:1px solid #eee;">${
    //             formData.name
    //           }</td></tr>
    //           <tr><td style="padding:8px 0; border-bottom:1px solid #eee;"><strong>Email:</strong></td><td style="padding:8px 0; border-bottom:1px solid #eee;">${
    //             formData.email
    //           }</td></tr>
    //           <tr><td style="padding:8px 0; border-bottom:1px solid #eee;"><strong>Pakket:</strong></td><td style="padding:8px 0; border-bottom:1px solid #eee;">${
    //             selectedPackage.name
    //           }</td></tr>
    //           <tr><td style="padding:8px 0; border-bottom:1px solid #eee;"><strong>Investering:</strong></td><td style="padding:8px 0; border-bottom:1px solid #eee;">€${selectedPackage.price.toLocaleString()}</td></tr>
    //         </table>
    //       </div>
    //       <div style="background:#f0f0f0; color:#555; padding:12px; font-size:12px; text-align:center;">
    //         Verstuurd op ${new Date().toLocaleString()}<br>
    //         Powered by Klusgebied
    //       </div>
    //     </div>
    //   `;
    //   let emailSent = false;
    //   let emailSentAt = null;
    //   try {
    //     const response = await fetch("https://api.heybossai.com/v1/run", {
    //       method: "POST",
    //       headers: {
    //         "Content-Type": "application/json",
    //         Authorization: `Bearer ${import.meta.env.VITE_API_KEY}`,
    //       },
    //       body: JSON.stringify({
    //         model: "aws/send-email",
    //         inputs: {
    //           receivers: config.user_email,
    //           title: emailSubject,
    //           body_html: emailHtml,
    //           project_id: config.project_id,
    //         },
    //       }),
    //     });
    //     const result = await response.json();
    //     if (result.send_email_status === "success") {
    //       emailSent = true;
    //       emailSentAt = new Date().toISOString();
    //     }
    //   } catch (emailError) {
    //     console.error("Email sending failed:", emailError);
    //   }
    //   // 3. Save to database
    //   const { error: dbError } = await supabase
    //     .from("6865560a10605c25f99173ec_investment_submissions")
    //     .insert({
    //       name: formData.name,
    //       email: formData.email,
    //       package_name: selectedPackage.name,
    //       investment_amount: selectedPackage.price,
    //       email_sent: emailSent,
    //       email_sent_at: emailSentAt,
    //     });
    //   if (dbError) {
    //     console.error("Database error:", dbError);
    //     setSubmitStatus("error");
    //   } else {
    //     setSubmitStatus("success");
    //   }
    // } catch (error) {
    //   console.error("Submission error:", error);
    //   setSubmitStatus("error");
    // } finally {
    //   setIsSubmitting(false);
    // }
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedPackage(null);
    setFormData({ name: "", email: "" });
    setSubmitStatus("");
  };

  return (
    <>
      <section className="py-24 bg-gray-50">
        <div className="container mx-auto px-6">
          {/* Header */}
          <div className="text-center mb-16">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-teal-100 rounded-full mb-6">
              <span className="text-2xl">📈</span>
            </div>
            <h2 className="text-4xl font-bold text-gray-900 mb-6 tracking-wide leading-relaxed">
              Klusgebied Abonnementen
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed tracking-wide">
              Kies het abonnement dat perfect bij jouw bedrijf past. Van basis
              zichtbaarheid tot volledige partnerschap met exclusieve voordelen.
              Alle abonnementen helpen je meer klanten te bereiken en je bedrijf
              te laten groeien.
            </p>
          </div>

          {/* Subscription Tiers Cards */}
          <div className="grid lg:grid-cols-3 gap-8 mb-16 max-w-7xl mx-auto">
            {subscriptionTiers.map((tier, index) => {
              const IconComponent = tier.icon;
              return (
                <div
                  key={tier.id}
                  className={`
                    relative ${tier.bgColor} ${tier.borderColor}
                    border-2 rounded-xl p-8 motion-preset-slide-up motion-delay-${
                      index * 100
                    }
                    ${tier.popular ? "ring-4 ring-teal-200 scale-105" : ""}
                  `}
                >
                  {tier.popular && (
                    <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                      <span className="bg-teal-500 text-white px-4 py-1 rounded-full text-sm font-semibold">
                        Meest Gekozen
                      </span>
                    </div>
                  )}

                  <div className="text-center mb-8">
                    <div
                      className={`inline-flex items-center justify-center w-16 h-16 ${tier.bgColor} rounded-full mb-4`}
                    >
                      <IconComponent className={`w-8 h-8 ${tier.textColor}`} />
                    </div>
                    <h3 className="text-2xl font-bold text-gray-900 mb-2 tracking-wide">
                      {tier.name}
                    </h3>
                    <div className="text-4xl font-bold text-gray-900 mb-1">
                      €{tier.price}
                    </div>
                    <p className="text-gray-600 text-lg tracking-wide">
                      {tier.period}
                    </p>
                  </div>

                  <button
                    onClick={() => handlePackageSelect(tier)}
                    className={`
                      w-full ${tier.buttonColor} text-white font-semibold py-4 px-6 rounded-lg
                      transition-all duration-200 flex items-center justify-center group text-lg tracking-wide
                    `}
                  >
                    Kies {tier.name}
                    <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
                  </button>
                </div>
              );
            })}
          </div>

          {/* Detailed Comparison Table */}
          <div className="mb-16">
            <div className="text-center mb-12">
              <h3 className="text-3xl font-bold text-gray-900 mb-4 tracking-wide leading-relaxed">
                Vergelijk alle functies
              </h3>
              <p className="text-lg text-gray-600 max-w-2xl mx-auto leading-relaxed tracking-wide">
                Bekijk in detail wat elk abonnement biedt en maak de beste keuze
                voor jouw bedrijf.
              </p>
            </div>

            <div className="bg-white rounded-2xl shadow-xl overflow-hidden border border-gray-200">
              <div className="overflow-x-auto">
                <table className="w-full text-left">
                  <thead className="bg-gray-100">
                    <tr>
                      <th className="p-6 text-lg font-bold text-gray-900 tracking-wide">
                        Functie
                      </th>
                      <th className="p-6 text-center text-lg font-bold text-blue-600 tracking-wide">
                        Klusgebied+
                      </th>
                      <th className="p-6 text-center text-lg font-bold text-teal-600 bg-teal-50/50 border-x-2 border-teal-500 tracking-wide">
                        Klusgebied Pro
                      </th>
                      <th className="p-6 text-center text-lg font-bold text-purple-600 tracking-wide">
                        Klusgebied Partner
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {comparisonFeatures.map((row, index) => (
                      <tr
                        key={index}
                        className="border-b border-gray-200 last:border-b-0 hover:bg-gray-50/50 transition-colors duration-200"
                      >
                        <td className="p-5 font-semibold text-gray-800 text-lg tracking-wide">
                          {row.feature}
                        </td>
                        <td className="p-5 text-center text-base leading-relaxed">
                          {renderCell(row.plus)}
                        </td>
                        <td className="p-5 text-center bg-teal-50/30 border-x-2 border-teal-500 text-base leading-relaxed">
                          {renderCell(row.pro)}
                        </td>
                        <td className="p-5 text-center text-base leading-relaxed">
                          {renderCell(row.partner)}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>

          {/* Call to Action */}
          <div className="text-center mt-16">
            <p className="text-gray-600 mb-6 text-lg tracking-wide leading-relaxed">
              Vragen over onze abonnementen of hulp nodig bij het kiezen?
            </p>
            <button
              onClick={() => navigate("/contact")}
              className="bg-gray-800 hover:bg-gray-900 text-white font-semibold px-8 py-3 rounded-lg transition-colors text-lg tracking-wide"
            >
              Neem Contact Op
            </button>
          </div>
        </div>
      </section>

      {/* Investment Interest Modal */}
      {showModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-xl p-8 max-w-lg w-full max-h-[90vh] overflow-y-auto">
            <div className="flex justify-between items-center mb-6">
              <h3 className="text-2xl font-bold text-gray-900 tracking-wide">
                Interesse in {selectedPackage?.name}
              </h3>
              <button
                onClick={closeModal}
                className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
                disabled={isSubmitting}
              >
                <X className="w-6 h-6" />
              </button>
            </div>

            {submitStatus === "success" && (
              <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center">
                  <CheckCircle className="w-5 h-5 text-green-500 mr-3" />
                  <div>
                    <p className="font-semibold text-green-800">
                      Interesse geregistreerd!
                    </p>
                    <p className="text-green-700 text-sm">
                      We nemen binnenkort contact met je op.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {submitStatus === "duplicate" && (
              <div className="mb-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center">
                  <AlertTriangle className="w-5 h-5 text-yellow-500 mr-3" />
                  <div>
                    <p className="font-semibold text-yellow-800">
                      Al geregistreerd
                    </p>
                    <p className="text-yellow-700 text-sm">
                      Je hebt al interesse getoond voor dit pakket.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {submitStatus === "error" && (
              <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
                <div className="flex items-center">
                  <AlertTriangle className="w-5 h-5 text-red-500 mr-3" />
                  <div>
                    <p className="font-semibold text-red-800">
                      Er ging iets mis
                    </p>
                    <p className="text-red-700 text-sm">
                      Probeer het later opnieuw of neem contact op.
                    </p>
                  </div>
                </div>
              </div>
            )}

            {submitStatus !== "success" && (
              <form onSubmit={handleSubmit} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Volledige Naam *
                  </label>
                  <div className="relative">
                    <User className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      disabled={isSubmitting}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent disabled:opacity-50"
                      placeholder="Jouw naam"
                    />
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Email Adres *
                  </label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      disabled={isSubmitting}
                      className="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-transparent disabled:opacity-50"
                      placeholder="<EMAIL>"
                    />
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-gray-900 mb-2 text-lg tracking-wide">
                    {selectedPackage?.name} - €{selectedPackage?.price}{" "}
                    {selectedPackage?.period}
                  </h4>
                  <p className="text-sm text-gray-600 leading-relaxed tracking-wide">
                    Je toont interesse in het {selectedPackage?.name}{" "}
                    abonnement. We nemen binnenkort contact met je op om de
                    mogelijkheden te bespreken en je te helpen bij het opstarten
                    van je abonnement.
                  </p>
                </div>

                <div className="flex gap-4">
                  <button
                    type="button"
                    onClick={closeModal}
                    disabled={isSubmitting}
                    className="flex-1 px-6 py-3 border border-gray-300 text-gray-700 font-semibold rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50"
                  >
                    Annuleren
                  </button>
                  <button
                    type="submit"
                    disabled={isSubmitting || !formData.name || !formData.email}
                    className="flex-1 bg-teal-500 hover:bg-teal-600 text-white font-semibold px-6 py-3 rounded-lg transition-colors disabled:opacity-50 flex items-center justify-center"
                  >
                    {isSubmitting ? (
                      <>
                        <Loader className="w-5 h-5 mr-2 animate-spin" />
                        Versturen...
                      </>
                    ) : (
                      "Interesse Tonen"
                    )}
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      )}
    </>
  );
};

export default InvestmentSection;
